/**
 * Application Configuration
 *
 * This file centralizes all environment variables and configuration settings.
 * It provides a single source of truth for application configuration.
 */

// Configuration object
const config = {
  ENV: import.meta.env.VITE_ENV,
  API_URL: import.meta.env.VITE_API_URL,
  AUTHENTICATION_URL: import.meta.env.VITE_AUTHENTICATION_URL,
  USER_SERVICE_URL: import.meta.env.VITE_USER_SERVICE_URL,
  STUDENT_SERVICE_URL: import.meta.env.VITE_STUDENT_SERVICE_URL,
  COURSE_SERVICE_URL: import.meta.env.VITE_COURSE_SERVICE_URL,
  PARENT_SERVICE_URL: import.meta.env.PARENT_SERVICE_URL,


  // Authentication settings (for backward compatibility)
  AUTH: {
    TOKEN_KEY: 'token',
    USER_KEY: 'user',
    MAIN_CODE_KEY: 'main_code',
  },
};

export default config;
