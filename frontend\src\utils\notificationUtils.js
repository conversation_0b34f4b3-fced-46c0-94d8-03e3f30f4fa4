/**
 * Notification Utilities
 *
 * Utility functions for handling different types of notifications and messages.
 * Helps categorize messages and determine appropriate display behavior.
 */

/**
 * Determine the type of message based on its content
 * @param {string} message - The message to categorize
 * @returns {string} - The message type: 'success', 'validation', 'error', or 'hidden'
 */
export const getMessageType = (message) => {
  if (!message || typeof message !== 'string') {
    return 'error';
  }

  const lowerMessage = message.toLowerCase();

  // Check if message contains success indicators
  const successKeywords = [
    'successfully', 'success', 'completed', 'saved', 'created', 
    'updated', 'deleted', 'registered', 'mapped', 'sent'
  ];
  const isSuccess = successKeywords.some(keyword => 
    lowerMessage.includes(keyword)
  );

  // Check if message is a validation message
  const validationKeywords = [
    'already exists', 'must contain', 'required', 'invalid', 'minimum', 'maximum',
    'password must', 'username must', 'email must', 'field is required',
    'does not match', 'too short', 'too long', 'format is invalid',
    'enter a valid', 'please provide', 'cannot be empty', 'is not valid'
  ];
  const isValidation = validationKeywords.some(keyword => 
    lowerMessage.includes(keyword)
  );

  // Check if message is a generic error that should be hidden
  const genericErrorKeywords = [
    'internal server error', 'something went wrong', 'unexpected error',
    'server error', 'network error', 'connection failed', 'timeout',
    'service unavailable', 'bad gateway', 'gateway timeout', 'error 500',
    'error 502', 'error 503', 'error 504'
  ];
  const isGenericError = genericErrorKeywords.some(keyword => 
    lowerMessage.includes(keyword)
  );

  // Return appropriate type
  if (isGenericError) {
    return 'hidden';
  } else if (isSuccess) {
    return 'success';
  } else if (isValidation) {
    return 'validation';
  } else {
    return 'error';
  }
};

/**
 * Check if a message should be shown to the user
 * @param {string} message - The message to check
 * @returns {boolean} - Whether the message should be displayed
 */
export const shouldShowMessage = (message) => {
  return getMessageType(message) !== 'hidden';
};

/**
 * Get the appropriate notification method name for a message
 * @param {string} message - The message to categorize
 * @returns {string} - The method name: 'showSuccess', 'showValidation', 'showError'
 */
export const getNotificationMethod = (message) => {
  const type = getMessageType(message);
  
  switch (type) {
    case 'success':
      return 'showSuccess';
    case 'validation':
      return 'showValidation';
    case 'error':
      return 'showError';
    case 'hidden':
      return null; // Don't show hidden messages
    default:
      return 'showError';
  }
};

/**
 * Common validation messages that should be shown as validation type
 */
export const VALIDATION_MESSAGES = {
  USERNAME_EXISTS: 'Username already exists',
  EMAIL_EXISTS: 'Email already exists',
  PASSWORD_DIGIT: 'Password must contain at least one digit',
  PASSWORD_UPPERCASE: 'Password must contain at least one uppercase letter',
  PASSWORD_LOWERCASE: 'Password must contain at least one lowercase letter',
  PASSWORD_SPECIAL: 'Password must contain at least one special character',
  PASSWORD_LENGTH: 'Password must be at least 8 characters long',
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_USERNAME: 'Invalid username format'
};

/**
 * Common success messages
 */
export const SUCCESS_MESSAGES = {
  USER_REGISTERED: 'User registered successfully',
  LOGIN_SUCCESS: 'Login successful',
  PASSWORD_RESET: 'Password reset successfully',
  DATA_SAVED: 'Data saved successfully',
  EMAIL_SENT: 'Email sent successfully'
};
