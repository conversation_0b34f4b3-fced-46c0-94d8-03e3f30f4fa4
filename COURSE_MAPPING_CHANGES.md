# Student-Course Mapping Enhancement

## Overview
This document describes the changes made to implement the functionality where mapping a student to a course also stores the course name in the student table.

## Problem Statement
Previously, when a student was mapped to a course:
1. The mapping was stored in the `student_courses` table in `course_db`
2. The student table in `student_db` did not have any course information
3. To get a student's course, you had to query the mapping table

## Solution Implemented
Now when a student is mapped to a course:
1. The mapping is still stored in the `student_courses` table (for many-to-many relationship support)
2. The course name is also stored directly in the `course` column of the student table
3. This provides easy access to course information when querying student data

## Files Modified

### 1. Student Service Changes

#### `student_service/models.py`
- **Added**: `course` column to Student model
- **Updated**: `__init__` method to accept course parameter
- **Updated**: `to_dict` method to include course in response

```python
# New column added
course = db.Column(db.String(100), nullable=True)  # Store course name when mapped

# Updated constructor
def __init__(self, user_id, first_name, last_name, date_of_birth=None, address=None, phone=None, course=None):

# Updated to_dict method includes course field
'course': self.course,
```

#### `student_service/controllers.py`
- **Added**: `update_student_course()` function to update student's course information
- **Features**: 
  - Validates required fields (student_id, course_name)
  - Checks user permissions
  - Updates student's course field and updated_at timestamp

#### `student_service/views.py`
- **Added**: Import for `update_student_course` function
- **Added**: New endpoint `/update-course` (PUT method) to handle course updates

### 2. Course Service Changes

#### `course_service/controllers.py`
- **Modified**: `map_student_to_course()` function
- **Added**: Inter-service communication to update student's course field
- **Features**:
  - After creating the mapping in `student_courses` table
  - Calls Student Service API to update the course field
  - Handles errors gracefully (mapping succeeds even if student service update fails)
  - Uses existing `call_service` utility function

```python
# New code added to map_student_to_course function
# Update student's course field in Student Service
try:
    student_service_url = request.environ.get('STUDENT_SERVICE_URL', 'http://localhost:5002')
    update_course_url = f"{student_service_url}/api/students/update-course"
    
    # Prepare data for student service
    update_data = {
        "student_id": data['student_id'],
        "course_name": course.name
    }
    
    # Call the Student Service to update course
    response = call_service(update_course_url, headers=headers, method='PUT', data=update_data)
except Exception as e:
    print(f"Warning: Error updating student course in Student Service: {str(e)}")
    # Don't fail the mapping if student service update fails
```

## Database Schema Changes

### Student Table (student_db)
```sql
-- New column added
ALTER TABLE students ADD COLUMN course VARCHAR(100);
```

**Note**: The column will be automatically created when the student service starts due to SQLAlchemy's `db.create_all()` functionality.

## API Endpoints

### New Endpoint
- **PUT** `/api/students/update-course`
  - **Purpose**: Update a student's course information
  - **Required Fields**: `student_id`, `course_name`
  - **Permissions**: Super Admin, Admin, Teacher
  - **Response**: Updated student information

### Modified Behavior
- **POST** `/api/courses/map-student`
  - **Enhanced**: Now also updates the student's course field in addition to creating the mapping

## Data Flow

1. **Course Mapping Request**: POST to `/api/courses/map-student`
2. **Validation**: Check if course exists, student exists, mapping doesn't already exist
3. **Create Mapping**: Insert record into `student_courses` table
4. **Update Student**: Call Student Service to update course field
5. **Response**: Return mapping information

## Error Handling

- If the student service is unavailable, the mapping still succeeds
- Warnings are logged if the student course update fails
- The system is resilient to inter-service communication issues

## Testing

A test script `test_course_mapping.py` has been created to verify the functionality:

1. Creates a test course
2. Creates a test user and student
3. Maps the student to the course
4. Verifies that the course name appears in the student table
5. Verifies the mapping exists in the course service

## Benefits

1. **Performance**: Direct access to course information when querying students
2. **Simplicity**: No need for joins to get basic course information
3. **Backward Compatibility**: Existing mapping table functionality is preserved
4. **Resilience**: System continues to work even if inter-service communication fails

## Usage Example

```python
# After mapping a student to a course
student_data = {
    "id": 15,
    "user_id": 54,
    "first_name": "ram",
    "last_name": "bro",
    "course": "Jee Advanced",  # <- Course name now stored here
    # ... other fields
}
```

## Future Considerations

- If supporting multiple courses per student, consider storing course IDs instead of names
- Consider adding course_id field alongside course name for referential integrity
- Monitor performance impact of inter-service calls during high-volume operations
