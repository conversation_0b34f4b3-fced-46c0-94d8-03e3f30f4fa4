/**
 * Popup Demo Page
 *
 * A demo page to test the new centered popup notification system.
 * Shows different types of messages: success, validation, and error.
 */

import { useNotification } from '../contexts/NotificationContext';
import Button from '../components/field/Button';

const PopupDemo = () => {
  const { showSuccess, showValidation, showMessage } = useNotification();

  const handleSuccessClick = () => {
    showSuccess('User registered successfully! This is a success message.');
  };

  const handleValidationClick = () => {
    showValidation('Username already exists. Please choose a different username.');
  };

  const handlePasswordValidationClick = () => {
    showValidation('Password must contain at least one digit.');
  };

  const handleAutoDetectSuccess = () => {
    showMessage('Student registered successfully');
  };

  const handleAutoDetectValidation = () => {
    showMessage('Email already exists');
  };

  const handleAutoDetectError = () => {
    showMessage('Failed to connect to server');
  };

  const handleGenericError = () => {
    showMessage('Internal server error'); // This should be hidden
  };

  const handleMultipleMessages = () => {
    showSuccess('First success message');
    setTimeout(() => showValidation('Username already exists'), 500);
    setTimeout(() => showSuccess('Another success message'), 1000);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
            Popup Notification Demo
          </h1>
          
          <div className="space-y-8">
            <div>
              <h2 className="text-xl font-semibold text-gray-700 mb-4">Manual Message Types</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="success"
                  onClick={handleSuccessClick}
                  userRole="super_admin"
                  className="w-full"
                >
                  Show Success Message
                </Button>
                
                <Button
                  variant="danger"
                  onClick={handleValidationClick}
                  userRole="super_admin"
                  className="w-full"
                >
                  Show Validation Message
                </Button>
                
                <Button
                  variant="danger"
                  onClick={handlePasswordValidationClick}
                  userRole="super_admin"
                  className="w-full"
                >
                  Show Password Validation
                </Button>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-gray-700 mb-4">Auto-Detection (showMessage)</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="primary"
                  onClick={handleAutoDetectSuccess}
                  userRole="super_admin"
                  className="w-full"
                >
                  Auto Success
                </Button>
                
                <Button
                  variant="primary"
                  onClick={handleAutoDetectValidation}
                  userRole="super_admin"
                  className="w-full"
                >
                  Auto Validation
                </Button>
                
                <Button
                  variant="primary"
                  onClick={handleAutoDetectError}
                  userRole="super_admin"
                  className="w-full"
                >
                  Auto Error
                </Button>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-gray-700 mb-4">Special Cases</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  variant="secondary"
                  onClick={handleGenericError}
                  userRole="super_admin"
                  className="w-full"
                >
                  Generic Error (Hidden)
                </Button>
                
                <Button
                  variant="secondary"
                  onClick={handleMultipleMessages}
                  userRole="super_admin"
                  className="w-full"
                >
                  Multiple Messages
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-700 mb-2">Features Demonstrated:</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>✅ Centered popup positioning</li>
              <li>✅ Green color for success messages</li>
              <li>✅ Red color for validation messages</li>
              <li>✅ 5-second auto-dismiss</li>
              <li>✅ Generic error messages are hidden from users</li>
              <li>✅ Auto-detection of message types</li>
              <li>✅ Multiple popups stack properly</li>
              <li>✅ Smooth animations</li>
            </ul>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-700 mb-2">Message Categories:</h3>
            <div className="text-sm text-blue-600 space-y-1">
              <p><strong>Success:</strong> "successfully", "registered", "created", "saved", etc.</p>
              <p><strong>Validation:</strong> "already exists", "must contain", "required", "invalid", etc.</p>
              <p><strong>Hidden:</strong> "internal server error", "something went wrong", "network error", etc.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PopupDemo;
