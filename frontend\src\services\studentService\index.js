import axios from "axios";
import config from "../../config";
import { authService } from "../authService";

export const studentService = {
  // API Service Methods
  studentRegisterService: (data) => {
    return axios.post(`${config.API_URL}/api/students/students`, data);
  },
  studentGetAllService: () => {
    return axios.get(`${config.API_URL}/api/students/students`);
  },
  studentGetByIdService: (studentId) => {
    return axios.get(`${config.API_URL}/api/students/students/${studentId}`);
  },
  studentMapParentService: (data) => {
    return axios.post(`${config.API_URL}/api/students/map-parent`, data);
  },
  studentGetParentStudentsService: (parentId) => {
    return axios.get(`${config.API_URL}/api/students/parent-students/${parentId}`);
  },
  studentGetProfileService: () => {
    return axios.get(`${config.API_URL}/api/students/student-profile`);
  },


  // Utility Methods (for backward compatibility)
  registerStudent: async (studentData) => {
    try {
      const response = await axios.post(`${config.API_URL}/api/students/students`, studentData, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getStudents: async () => {
    try {
      const response = await axios.get(`${config.API_URL}/api/students/students`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getStudent: async (studentId) => {
    try {
      const response = await axios.get(`${config.API_URL}/api/students/students/${studentId}`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  mapParentToStudent: async (parentId, studentId, relationship = null) => {
    try {
      const response = await axios.post(`${config.API_URL}/api/students/map-parent`, {
        parent_id: parentId,
        student_id: studentId,
        relationship: relationship
      }, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getParentStudents: async (parentId) => {
    try {
      const response = await axios.get(`${config.API_URL}/api/students/parent-students/${parentId}`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getStudentProfile: async () => {
    try {
      const response = await axios.get(`${config.API_URL}/api/students/student-profile`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      try {
        const currentUser = authService.getCurrentUser();
        if (!currentUser) {
          throw { error: 'User not authenticated' };
        }

        const data = await studentService.getStudents();
        const students = data.students || [];
        const currentStudent = students.find(s => s.user_id === parseInt(currentUser.id));

        if (!currentStudent) {
          throw { error: 'Student not found' };
        }

        return { student: currentStudent };
      } catch (fallbackError) {
        throw fallbackError.error ? fallbackError : { error: 'Failed to get student profile' };
      }
    }
  },

  getStudentParents: async (studentId) => {
    try {
      const response = await axios.get(`${config.API_URL}/api/students/student-parents/${studentId}`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
};
