/**
 * Notification Context
 *
 * This context provides notification/toast functionality to the entire application.
 * Manages success and error notifications with auto-dismiss functionality.
 */

import { createContext, useState, useContext, useCallback } from 'react';
import ToastContainer from '../components/popup/ToastContainer';

// Create the context
const NotificationContext = createContext();

// Custom hook to use the notification context
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

// Provider component
export const NotificationProvider = ({ children, position = 'center' }) => {
  const [toasts, setToasts] = useState([]);

  // Generate unique ID for each toast
  const generateId = useCallback(() => {
    return Date.now() + Math.random().toString(36).substr(2, 9);
  }, []);

  // Add a new toast notification
  const addNotification = useCallback((message, type = 'success', duration = 5000) => {
    const id = generateId();
    const newToast = {
      id,
      message,
      type,
      duration,
      timestamp: Date.now()
    };

    setToasts(prevToasts => [...prevToasts, newToast]);
    return id;
  }, [generateId]);

  // Remove a toast notification
  const removeNotification = useCallback((id) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods for different types of notifications
  const showSuccess = useCallback((message, duration = 5000) => {
    return addNotification(message, 'success', duration);
  }, [addNotification]);

  const showError = useCallback((message, duration = 5000) => {
    return addNotification(message, 'error', duration);
  }, [addNotification]);

  // Show validation messages (like password requirements, username conflicts)
  const showValidation = useCallback((message, duration = 5000) => {
    return addNotification(message, 'validation', duration);
  }, [addNotification]);

  // Auto-detect message type based on content (for backward compatibility)
  const showMessage = useCallback((message, duration = 5000) => {
    // Check if message contains success indicators
    const successKeywords = ['successfully', 'success', 'completed', 'saved', 'created', 'updated', 'deleted', 'registered', 'mapped'];
    const isSuccess = successKeywords.some(keyword =>
      message.toLowerCase().includes(keyword.toLowerCase())
    );

    // Check if message is a validation message
    const validationKeywords = [
      'already exists', 'must contain', 'required', 'invalid', 'minimum', 'maximum',
      'password must', 'username must', 'email must', 'field is required',
      'does not match', 'too short', 'too long', 'format is invalid'
    ];
    const isValidation = validationKeywords.some(keyword =>
      message.toLowerCase().includes(keyword.toLowerCase())
    );

    // Check if message is a generic error that should be hidden
    const genericErrorKeywords = [
      'internal server error', 'something went wrong', 'unexpected error',
      'server error', 'network error', 'connection failed', 'timeout',
      'service unavailable', 'bad gateway', 'gateway timeout'
    ];
    const isGenericError = genericErrorKeywords.some(keyword =>
      message.toLowerCase().includes(keyword.toLowerCase())
    );

    // Don't show generic error messages to users
    if (isGenericError) {
      console.error('Generic error hidden from user:', message);
      return null;
    }

    let type;
    if (isSuccess) {
      type = 'success';
    } else if (isValidation) {
      type = 'validation';
    } else {
      type = 'error';
    }

    return addNotification(message, type, duration);
  }, [addNotification]);

  // Context value
  const value = {
    toasts,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showValidation,
    showMessage
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <ToastContainer 
        toasts={toasts} 
        onRemoveToast={removeNotification}
        position={position}
      />
    </NotificationContext.Provider>
  );
};

export default NotificationContext;
