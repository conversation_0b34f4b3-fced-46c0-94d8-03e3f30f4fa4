import axios from "axios";
import config from "../../config";
import { authService } from "../authService";

export const userService = {
  // API Service Methods
  userRegisterService: (data) => {
    return axios.post(`${config.API_URL}/api/users/register`, data);
  },
  userGetAllService: () => {
    return axios.get(`${config.API_URL}/api/users/users`);
  },
  userGetByIdService: (userId) => {
    return axios.get(`${config.API_URL}/api/users/users/${userId}`);
  },

  // Utility Methods (for backward compatibility)
  registerUser: async (userData) => {
    try {
      console.log('Registering user with data:', userData);
      console.log('Using API URL:', `${config.API_URL}/api/users/register`);
      console.log('Auth headers:', authService.getAuthHeader());

      const response = await axios.post(`${config.API_URL}/api/users/register`, userData, {
        headers: {
          ...authService.getAuthHeader(),
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      console.log('User registration response:', response.data);
      return response.data;
    } catch (error) {
      console.error('User registration error:', error);
      if (error.response) {
        console.error('Error response:', error.response.status, error.response.data);
        throw error.response.data;
      } else if (error.request) {
        console.error('No response received:', error.request);
        throw { error: 'No response from server' };
      } else {
        console.error('Request setup error:', error.message);
        throw { error: error.message || 'Network error' };
      }
    }
  },

  getUsers: async () => {
    try {
      const response = await axios.get(`${config.API_URL}/api/users/users`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 403) {
        return { users: [] };
      }
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getUser: async (userId) => {
    try {
      const response = await axios.get(`${config.API_URL}/api/users/users/${userId}`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
};
