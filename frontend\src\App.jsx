/**
 * Main App Component
 *
 * This is the main component that handles routing and authentication.
 *
 * English: This component manages the application's routing and authentication
 * Tanglish: Indha component application-oda routing and authentication-a manage pannum
 */

import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { NotificationProvider } from '@/contexts/NotificationContext';
import ProtectedRoute from '@/components/common/ProtectedRoute';
import LoginPage from '@/pages/authentication/LoginPage';
import ForgetPasswordPage from '@/pages/authentication/ForgetPasswordPage';
import DashboardRouter from '@/pages/dashboard/DashboardRouter';
import PopupDemo from '@/pages/PopupDemo';

function App() {
  return (
    <AuthProvider>
      <NotificationProvider position="center">
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/forget-password" element={<ForgetPasswordPage />} />
          <Route path="/popup-demo" element={<PopupDemo />} />

          {/* Protected routes */}
          <Route
            path="/dashboard/*"
            element={
              <ProtectedRoute>
                <DashboardRouter />
              </ProtectedRoute>
            }
          />

          {/* Redirect to dashboard if logged in, otherwise to login */}
          <Route
            path="*"
            element={<Navigate to="/dashboard" />}
          />
        </Routes>
      </NotificationProvider>
    </AuthProvider>
  );
}

export default App;
