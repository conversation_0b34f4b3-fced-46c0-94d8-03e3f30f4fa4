"""
Models for the Student Service.

This module defines the database models for the Student Service.

English: This file defines the database tables for the Student Service
Tanglish: Indha file Student Service-kku database tables-a define pannum
"""

from datetime import datetime
from student_service.common.db_config import db

class Student(db.Model):
    """
    Student model.

    English: This model stores student information
    Tanglish: Indha model student information-a store pannum
    """
    __tablename__ = 'students'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False, unique=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    date_of_birth = db.Column(db.Date, nullable=True)
    address = db.Column(db.Text, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    course = db.Column(db.String(100), nullable=True)  # Store course name when mapped
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, first_name, last_name, date_of_birth=None, address=None, phone=None, course=None):
        """
        Initialize a new Student.

        Args:
            user_id: ID of the user associated with this student
            first_name: Student's first name
            last_name: Student's last name
            date_of_birth: Student's date of birth (optional)
            address: Student's address (optional)
            phone: Student's phone number (optional)
            course: Course name when mapped to a course (optional)

        English: This function creates a new student with the given information
        Tanglish: Indha function kudukkapatta information-oda puthusa oru student-a create pannum
        """
        self.user_id = user_id
        self.first_name = first_name
        self.last_name = last_name
        self.date_of_birth = date_of_birth
        self.address = address
        self.phone = phone
        self.course = course
        
    def to_dict(self):
        """
        Convert the student to a dictionary.

        Returns:
            Dictionary representation of the student

        English: This function converts the student to a dictionary
        Tanglish: Indha function student-a dictionary-a convert pannum
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'date_of_birth': self.date_of_birth.isoformat() if self.date_of_birth else None,
            'address': self.address,
            'phone': self.phone,
            'course': self.course,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ParentStudent(db.Model):
    """
    ParentStudent model for many-to-many relationship between parents and students.
    
    English: This model links parents to students (many-to-many)
    Tanglish: Indha model parents-a students-oda link pannum (many-to-many)
    """
    __tablename__ = 'parent_students'
    
    id = db.Column(db.Integer, primary_key=True)
    parent_id = db.Column(db.Integer, nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    relationship = db.Column(db.String(20), nullable=True)  # e.g., Father, Mother, Guardian
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Define relationship to Student
    student = db.relationship('Student', backref=db.backref('parent_students', lazy=True))
    
    def __init__(self, parent_id, student_id, relationship=None):
        """
        Initialize a new ParentStudent.
        
        Args:
            parent_id: ID of the parent
            student_id: ID of the student
            relationship: Relationship between parent and student (optional)
            
        English: This function links a parent to a student
        Tanglish: Indha function oru parent-a oru student-oda link pannum
        """
        self.parent_id = parent_id
        self.student_id = student_id
        self.relationship = relationship
        
    def to_dict(self):
        """
        Convert the parent student to a dictionary.
        
        Returns:
            Dictionary representation of the parent student
            
        English: This function converts the parent student to a dictionary
        Tanglish: Indha function parent student-a dictionary-a convert pannum
        """
        return {
            'id': self.id,
            'parent_id': self.parent_id,
            'student_id': self.student_id,
            'student': self.student.to_dict() if self.student else None,
            'relationship': self.relationship,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
