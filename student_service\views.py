"""
Views for the Student Service.

This module defines the API endpoints for the Student Service.

English: This file defines the API endpoints for the Student Service
Tanglish: Indha file Student Service-kku API endpoints-a define pannum
"""

from flask import Blueprint, jsonify, request
from student_service.controllers import (
    register_student, get_students, get_student,
    map_parent_to_student, get_parent_students, update_student_course
)
from student_service.models import Student

# Create a Blueprint for student routes
student_bp = Blueprint('student', __name__)

# Add route handlers for OPTIONS requests
@student_bp.route('/<path:path>', methods=['OPTIONS'])
def handle_options_path(path):
    """
    Handle OPTIONS requests for CORS preflight with path.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a handle pannum
    """
    return "", 200

@student_bp.route('/', methods=['OPTIONS'])
def handle_options_root():
    """
    Handle OPTIONS requests for CORS preflight at root.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS at root
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a root-la handle pannum
    """
    return "", 200

@student_bp.route('/students', methods=['POST'])
def register_student_route():
    """
    Register student endpoint.

    Returns:
        Response from register_student controller

    English: This endpoint registers a new student
    Tanglish: Indha endpoint puthusa oru student-a register pannum
    """
    return register_student()

@student_bp.route('/students', methods=['GET'])
def get_students_route():
    """
    Get all students endpoint.

    Returns:
        Response from get_students controller

    English: This endpoint gets all students
    Tanglish: Indha endpoint ella students-um get pannum
    """
    return get_students()

@student_bp.route('/students/<int:student_id>', methods=['GET'])
def get_student_route(student_id):
    """
    Get a specific student endpoint.

    Args:
        student_id: ID of the student to get

    Returns:
        Response from get_student controller

    English: This endpoint gets a specific student
    Tanglish: Indha endpoint specific student-a get pannum
    """
    return get_student(student_id)

@student_bp.route('/map-parent', methods=['POST'])
def map_parent_route():
    """
    Map parent to student endpoint.

    Returns:
        Response from map_parent_to_student controller

    English: This endpoint maps a parent to a student
    Tanglish: Indha endpoint oru parent-a oru student-oda map pannum
    """
    return map_parent_to_student()

@student_bp.route('/parent-students/<int:parent_id>', methods=['GET'])
def get_parent_students_route(parent_id):
    """
    Get all students for a specific parent endpoint.

    Args:
        parent_id: ID of the parent

    Returns:
        Response from get_parent_students controller

    English: This endpoint gets all students for a specific parent
    Tanglish: Indha endpoint specific parent-kku ella students-um get pannum
    """
    return get_parent_students(parent_id)

@student_bp.route('/update-course', methods=['PUT'])
def update_student_course_route():
    """
    Update student course endpoint.

    Returns:
        Response from update_student_course controller

    English: This endpoint updates a student's course information
    Tanglish: Indha endpoint oru student-oda course information-a update pannum
    """
    return update_student_course()

@student_bp.route('/student-profile', methods=['GET', 'OPTIONS'])
def get_student_profile():
    """
    Get the profile of the currently logged-in student.

    Returns:
        JSON response with the student's profile

    English: This endpoint gets the profile of the currently logged-in student
    Tanglish: Indha endpoint login panra student-oda profile-a get pannum
    """
    # Get user ID from request environment
    user_id = request.environ.get('user_id')
    user_role = request.environ.get('user_role')

    print(f"User ID from environment: '{user_id}'")
    print(f"User role from environment: '{user_role}'")

    if not user_id:
        return jsonify({"error": "User ID not found in token"}), 400

    # Get the student with the matching user_id
    student = Student.query.filter_by(user_id=user_id).first()

    if not student:
        return jsonify({"error": "Student not found"}), 404

    # Return the student's profile
    return jsonify({
        "student": student.to_dict()
    })

@student_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint.

    Returns:
        JSON response indicating the service is running

    English: This endpoint checks if the service is running
    Tanglish: Indha endpoint service odi kondu irukka nu check pannum
    """
    return jsonify({"status": "healthy", "service": "student"})
