"""
Test script to verify student-course mapping functionality.

This script tests the new functionality where mapping a student to a course
also updates the course field in the student table.

Usage:
    python test_course_mapping.py
"""

import requests
import json

# Configuration
BASE_URLS = {
    'auth': 'http://localhost:5000/api/auth',
    'users': 'http://localhost:5001/api/users',
    'students': 'http://localhost:5002/api/students',
    'courses': 'http://localhost:5003/api/courses',
    'parents': 'http://localhost:5004/api/parents'
}

# Test credentials
ADMIN_CREDENTIALS = {
    'username': 'superadmin',
    'password': 'D@qwertyuiop'
}

def get_auth_token():
    """Get authentication token."""
    try:
        response = requests.post(f"{BASE_URLS['auth']}/login", json=ADMIN_CREDENTIALS)
        if response.status_code == 200:
            return response.json().get('access_token')
        else:
            print(f"Failed to get auth token: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None

def make_authenticated_request(method, url, data=None, token=None):
    """Make an authenticated request."""
    headers = {'Content-Type': 'application/json'}
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=headers)
        else:
            print(f"Unsupported method: {method}")
            return None
        
        print(f"{method} {url}")
        print(f"Status: {response.status_code}")
        if response.text:
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text}")
        print("-" * 50)
        
        return response
    except Exception as e:
        print(f"Error making request: {e}")
        return None

def test_course_mapping_functionality():
    """Test the complete course mapping functionality."""
    print("=== Testing Student-Course Mapping with Course Field Update ===\n")
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("Failed to get authentication token. Exiting.")
        return
    
    print("✓ Authentication successful\n")
    
    # Step 1: Create a test course
    print("Step 1: Creating a test course...")
    course_data = {
        "name": "Test Course - Python Programming",
        "description": "A test course for Python programming"
    }
    course_response = make_authenticated_request('POST', f"{BASE_URLS['courses']}/courses", course_data, token)
    
    if not course_response or course_response.status_code != 201:
        print("Failed to create course. Exiting.")
        return
    
    course_id = course_response.json().get('course', {}).get('id')
    print(f"✓ Course created with ID: {course_id}\n")
    
    # Step 2: Create a test user for student
    print("Step 2: Creating a test user...")
    user_data = {
        "username": "teststudent123",
        "password": "TestPassword123!",
        "email": "<EMAIL>",
        "role": "Student"
    }
    user_response = make_authenticated_request('POST', f"{BASE_URLS['users']}/users", user_data, token)
    
    if not user_response or user_response.status_code != 201:
        print("Failed to create user. Exiting.")
        return
    
    user_id = user_response.json().get('user', {}).get('id')
    print(f"✓ User created with ID: {user_id}\n")
    
    # Step 3: Register the student
    print("Step 3: Registering the student...")
    student_data = {
        "user_id": user_id,
        "first_name": "Test",
        "last_name": "Student",
        "date_of_birth": "2005-01-15",
        "address": "123 Test Street",
        "phone": "1234567890"
    }
    student_response = make_authenticated_request('POST', f"{BASE_URLS['students']}/students", student_data, token)
    
    if not student_response or student_response.status_code != 201:
        print("Failed to register student. Exiting.")
        return
    
    student_id = student_response.json().get('student', {}).get('id')
    print(f"✓ Student registered with ID: {student_id}\n")
    
    # Step 4: Check student before mapping (should have no course)
    print("Step 4: Checking student before course mapping...")
    student_before = make_authenticated_request('GET', f"{BASE_URLS['students']}/students/{student_id}", None, token)
    
    if student_before and student_before.status_code == 200:
        course_before = student_before.json().get('student', {}).get('course')
        print(f"Student course before mapping: {course_before}")
    
    # Step 5: Map student to course
    print("\nStep 5: Mapping student to course...")
    mapping_data = {
        "student_id": student_id,
        "course_id": course_id
    }
    mapping_response = make_authenticated_request('POST', f"{BASE_URLS['courses']}/map-student", mapping_data, token)
    
    if not mapping_response or mapping_response.status_code != 201:
        print("Failed to map student to course.")
        return
    
    print("✓ Student mapped to course successfully\n")
    
    # Step 6: Check student after mapping (should have course name)
    print("Step 6: Checking student after course mapping...")
    student_after = make_authenticated_request('GET', f"{BASE_URLS['students']}/students/{student_id}", None, token)
    
    if student_after and student_after.status_code == 200:
        course_after = student_after.json().get('student', {}).get('course')
        print(f"Student course after mapping: {course_after}")
        
        if course_after == course_data['name']:
            print("✅ SUCCESS: Course field updated correctly in student table!")
        else:
            print("❌ FAILED: Course field not updated correctly in student table.")
    
    # Step 7: Verify mapping in course service
    print("\nStep 7: Verifying mapping in course service...")
    courses_for_student = make_authenticated_request('GET', f"{BASE_URLS['courses']}/student-courses/{student_id}", None, token)
    
    if courses_for_student and courses_for_student.status_code == 200:
        courses = courses_for_student.json().get('courses', [])
        if courses:
            print(f"✓ Found {len(courses)} course(s) mapped to student")
        else:
            print("❌ No courses found for student")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_course_mapping_functionality()
